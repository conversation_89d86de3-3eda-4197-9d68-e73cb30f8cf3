import { SELF, env } from 'cloudflare:test';
import { describe, expect, it, vi, beforeEach, beforeAll, afterAll } from 'vitest';
import { ApplicationD1 } from '../../../db/applications.js';
import { SendToQueue } from '../../../queues/index.js';
import { getAppPII } from '../../../kv/index.js';
import * as kvModule from '../../../kv/index.js';
import { generateCreateAppRequest, generateApplicationData } from '../../../../test/testUtils.js';

const BASE_URL = 'http://localhost:8787';

describe('Submit App Handler', () => {
  let testAppUuid;
  const ogSalesforce = SendToQueue.salseforce;

  beforeAll(async () => {
    SendToQueue.salseforce = vi.fn();

    // Create a test application first
    const requestData = generateCreateAppRequest();
    const createResponse = await SELF.fetch(`${BASE_URL}/app`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestData),
    });
    const createData = await createResponse.json();
    testAppUuid = createData.uuid;

    // Update application to APP_STARTED status to allow submission
    await ApplicationD1.update(env, testAppUuid, {
      status: 'APP_STARTED',
    });
  });

  afterAll(async () => {
    SendToQueue.salseforce = ogSalesforce;
  });

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('submits application with valid data and updates status to APP_SUBMITTED', async () => {
    const applicationFields = generateApplicationData();

    const response = await SELF.fetch(`${BASE_URL}/app/${testAppUuid}/submit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ applicationFields }),
    });

    expect(response.status).toBe(200);

    const data = await response.json();
    expect(data.data).toMatchObject({
      uuid: testAppUuid,
      status: 'APP_SUBMITTED',
      preQualifyFields: expect.objectContaining({
        firstName: expect.any(String),
        businessName: expect.any(String),
        estimatedFICO: expect.any(String),
      }),
    });

    // Verify application was updated in database
    const savedApp = await ApplicationD1.get(env, testAppUuid);
    expect(savedApp.status).toBe('APP_SUBMITTED');
    expect(savedApp.submitted_at).toBeDefined();
    expect(savedApp.meta.submitted).toBeDefined();
    expect(savedApp.applicationFields).toMatchObject({
      businessName: applicationFields.businessName,
      entityType: applicationFields.entityType,
      industry: applicationFields.industry,
      owners: expect.arrayContaining([
        expect.objectContaining({
          firstName: applicationFields.owners[0].firstName,
          lastName: applicationFields.owners[0].lastName,
          email: applicationFields.owners[0].email,
        }),
      ]),
    });

    // Verify PII data was stored separately
    expect(savedApp.applicationFields.ein).toBeUndefined();
    expect(savedApp.applicationFields.owners[0].ssn).toBeUndefined();
    expect(savedApp.applicationFields.owners[0].dateOfBirth).toBeUndefined();

    // Verify Salesforce queue was called
    expect(SendToQueue.salseforce).toHaveBeenCalledWith(env, {
      application: expect.objectContaining({
        uuid: testAppUuid,
        status: 'APP_SUBMITTED',
      }),
    });
  });

  it('stores PII data in KV storage with correct structure', async () => {
    const applicationFields = generateApplicationData({
      ein: '987654321',
      owners: [
        {
          firstName: 'Jane',
          lastName: 'Doe',
          dateOfBirth: '1985-05-15',
          ssn: '987654321',
          email: '<EMAIL>',
          phone: '5551234567',
          address: {
            line1: '789 Main St',
            city: 'Test City',
            state: 'TX',
            zip: '75001',
          },
          ownershipPercentage: 100,
        },
      ],
    });

    const response = await SELF.fetch(`${BASE_URL}/app/${testAppUuid}/submit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ applicationFields }),
    });

    expect(response.status).toBe(200);

    // Verify PII data was stored correctly
    const storedPII = await getAppPII(env, testAppUuid);
    expect(storedPII).toMatchObject({
      ein: '987654321',
      owners: [
        {
          dateOfBirth: '1985-05-15',
          ssn: '987654321',
        },
      ],
      version: env.VERSION,
    });
  });

  it('validates required fields and returns validation error for missing business name', async () => {
    const invalidApplicationFields = generateApplicationData({
      businessName: '', // Invalid - too short
    });

    const response = await SELF.fetch(`${BASE_URL}/app/${testAppUuid}/submit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ applicationFields: invalidApplicationFields }),
    });

    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data.error).toContain('Validation Error');
  });

  it('validates required fields and returns validation error for invalid EIN format', async () => {
    const invalidApplicationFields = generateApplicationData({
      ein: '12345', // Invalid - wrong format
    });

    const response = await SELF.fetch(`${BASE_URL}/app/${testAppUuid}/submit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ applicationFields: invalidApplicationFields }),
    });

    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data.error).toContain('Validation Error');
  });

  it('validates required fields and returns validation error for missing owners', async () => {
    const invalidApplicationFields = generateApplicationData({
      owners: [], // Invalid - at least one owner required
    });

    const response = await SELF.fetch(`${BASE_URL}/app/${testAppUuid}/submit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ applicationFields: invalidApplicationFields }),
    });

    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data.error).toContain('Validation Error');
  });

  it('validates required fields and returns validation error for invalid SSN format', async () => {
    const invalidApplicationFields = generateApplicationData({
      owners: [
        {
          ...generateApplicationData().owners[0],
          ssn: '***********', // Invalid - should be 9 digits without dashes
        },
      ],
    });

    const response = await SELF.fetch(`${BASE_URL}/app/${testAppUuid}/submit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ applicationFields: invalidApplicationFields }),
    });

    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data.error).toContain('Validation Error');
  });

  it('validates address fields and returns validation error for invalid state', async () => {
    const invalidApplicationFields = generateApplicationData({
      address: {
        line1: '123 Test St',
        city: 'Test City',
        state: 'INVALID', // Invalid - should be 2 characters
        zip: '12345',
      },
    });

    const response = await SELF.fetch(`${BASE_URL}/app/${testAppUuid}/submit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ applicationFields: invalidApplicationFields }),
    });

    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data.error).toContain('Validation Error');
  });

  it('validates email format and returns validation error for invalid business email', async () => {
    const invalidApplicationFields = generateApplicationData({
      businessEmail: 'invalid-email', // Invalid email format
    });

    const response = await SELF.fetch(`${BASE_URL}/app/${testAppUuid}/submit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ applicationFields: invalidApplicationFields }),
    });

    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data.error).toContain('Validation Error');
  });

  it('handles missing applicationFields in request body', async () => {
    const response = await SELF.fetch(`${BASE_URL}/app/${testAppUuid}/submit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({}), // Missing applicationFields
    });

    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data.error).toContain('Validation Error');
  });

  it('handles database errors gracefully', async () => {
    vi.spyOn(ApplicationD1, 'update').mockRejectedValueOnce(new Error('Database connection failed'));

    const applicationFields = generateApplicationData();

    const response = await SELF.fetch(`${BASE_URL}/app/${testAppUuid}/submit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ applicationFields }),
    });

    expect(response.status).toBe(500);

    const data = await response.json();
    expect(data.error).toBe('Internal Server Error');
    expect(data.errorId).toBeDefined();
  });

  it('handles KV storage errors gracefully but still updates database', async () => {
    vi.spyOn(kvModule, 'putAppPII').mockRejectedValueOnce(new Error('KV storage failed'));

    const applicationFields = generateApplicationData();

    const response = await SELF.fetch(`${BASE_URL}/app/${testAppUuid}/submit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ applicationFields }),
    });

    expect(response.status).toBe(500);

    const data = await response.json();
    expect(data.error).toBe('Internal Server Error');
  });

  it('handles queue errors gracefully but still updates application', async () => {
    vi.spyOn(SendToQueue, 'salseforce').mockRejectedValueOnce(new Error('Queue failed'));

    const applicationFields = generateApplicationData();

    const response = await SELF.fetch(`${BASE_URL}/app/${testAppUuid}/submit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ applicationFields }),
    });

    expect(response.status).toBe(500);

    const data = await response.json();
    expect(data.error).toBe('Internal Server Error');
  });

  it('handles applications with two owners correctly', async () => {
    const applicationFields = generateApplicationData({
      owners: [
        {
          firstName: 'Owner',
          lastName: 'One',
          dateOfBirth: '1980-01-01',
          ssn: '111111111',
          email: '<EMAIL>',
          phone: '1111111111',
          address: {
            line1: '111 First St',
            city: 'First City',
            state: 'CA',
            zip: '11111',
          },
          ownershipPercentage: 60,
        },
        {
          firstName: 'Owner',
          lastName: 'Two',
          dateOfBirth: '1985-02-02',
          ssn: '222222222',
          email: '<EMAIL>',
          phone: '2222222222',
          address: {
            line1: '222 Second St',
            city: 'Second City',
            state: 'TX',
            zip: '22222',
          },
          ownershipPercentage: 40,
        },
      ],
    });

    const response = await SELF.fetch(`${BASE_URL}/app/${testAppUuid}/submit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ applicationFields }),
    });

    expect(response.status).toBe(200);

    // Verify PII data for both owners was stored correctly
    const storedPII = await getAppPII(env, testAppUuid);
    expect(storedPII.owners).toHaveLength(2);
    expect(storedPII.owners[0]).toMatchObject({
      dateOfBirth: '1980-01-01',
      ssn: '111111111',
    });
    expect(storedPII.owners[1]).toMatchObject({
      dateOfBirth: '1985-02-02',
      ssn: '222222222',
    });

    // Verify sanitized application fields don't contain PII
    const savedApp = await ApplicationD1.get(env, testAppUuid);
    expect(savedApp.applicationFields.owners).toHaveLength(2);
    expect(savedApp.applicationFields.owners[0].ssn).toBeUndefined();
    expect(savedApp.applicationFields.owners[0].dateOfBirth).toBeUndefined();
    expect(savedApp.applicationFields.owners[1].ssn).toBeUndefined();
    expect(savedApp.applicationFields.owners[1].dateOfBirth).toBeUndefined();
  });

  it('validates ownership percentage limits', async () => {
    const invalidApplicationFields = generateApplicationData({
      owners: [
        {
          ...generateApplicationData().owners[0],
          ownershipPercentage: 150, // Invalid - over 100%
        },
      ],
    });

    const response = await SELF.fetch(`${BASE_URL}/app/${testAppUuid}/submit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ applicationFields: invalidApplicationFields }),
    });

    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data.error).toContain('Validation Error');
  });
});
