import { SELF, env } from 'cloudflare:test';
import { describe, expect, it, vi, beforeEach, beforeAll, afterAll } from 'vitest';
import { ApplicationD1 } from '../../../db/applications.js';
import { SendToQueue } from '../../../queues/index.js';
import { getAppPII } from '../../../kv/index.js';
import * as kvModule from '../../../kv/index.js';
import { generateCreateAppRequest, generateApplicationData, generateSignedApplicationData } from '../../../../test/testUtils.js';

const BASE_URL = 'http://localhost:8787';

describe('Sign App Handler', () => {
  let testAppUuid;
  const ogSalesforce = SendToQueue.salseforce;
  const ogEmail = SendToQueue.email;

  beforeAll(async () => {
    SendToQueue.salseforce = vi.fn();
    SendToQueue.email = vi.fn();

    // Create a test application first
    const requestData = generateCreateAppRequest();
    const createResponse = await SELF.fetch(`${BASE_URL}/app`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestData),
    });
    const createData = await createResponse.json();
    testAppUuid = createData.uuid;

    // Submit the application first to get it to APP_SUBMITTED status
    const applicationFields = generateApplicationData();
    await ApplicationD1.update(env, testAppUuid, { status: 'APP_STARTED' });

    await SELF.fetch(`${BASE_URL}/app/${testAppUuid}/submit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ applicationFields }),
    });
  });

  afterAll(async () => {
    SendToQueue.salseforce = ogSalesforce;
    SendToQueue.email = ogEmail;
  });

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('signs application with valid data and updates status to APP_SIGNED', async () => {
    const signedApplicationFields = generateSignedApplicationData();

    const response = await SELF.fetch(`${BASE_URL}/app/${testAppUuid}/sign`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ applicationFields: signedApplicationFields }),
    });

    expect(response.status).toBe(200);

    const data = await response.json();
    expect(data.data).toMatchObject({
      uuid: testAppUuid,
      status: 'APP_SIGNED',
      preQualifyFields: expect.objectContaining({
        firstName: expect.any(String),
        businessName: expect.any(String),
      }),
    });

    // Verify application was updated in database
    const savedApp = await ApplicationD1.get(env, testAppUuid);
    expect(savedApp.status).toBe('APP_SIGNED');
    expect(savedApp.signed_at).toBeDefined();
    expect(savedApp.meta.signed).toBeDefined();
    expect(savedApp.applicationFields).toMatchObject({
      businessName: signedApplicationFields.businessName,
      entityType: signedApplicationFields.entityType,
      industry: signedApplicationFields.industry,
      owners: expect.arrayContaining([
        expect.objectContaining({
          firstName: signedApplicationFields.owners[0].firstName,
          lastName: signedApplicationFields.owners[0].lastName,
          email: signedApplicationFields.owners[0].email,
        }),
      ]),
    });

    // Verify PII data was stored separately and signature is not in sanitized fields
    expect(savedApp.applicationFields.ein).toBeUndefined();
    expect(savedApp.applicationFields.owners[0].ssn).toBeUndefined();
    expect(savedApp.applicationFields.owners[0].dateOfBirth).toBeUndefined();

    // Verify both email and Salesforce queues were called
    expect(SendToQueue.email).toHaveBeenCalledWith(env, {
      application: expect.objectContaining({
        uuid: testAppUuid,
        status: 'APP_SIGNED',
      }),
    });
    expect(SendToQueue.salseforce).toHaveBeenCalledWith(env, {
      application: expect.objectContaining({
        uuid: testAppUuid,
        status: 'APP_SIGNED',
      }),
    });
  });

  it('stores PII data including credit score in KV storage', async () => {
    const signedApplicationFields = generateSignedApplicationData({
      ein: '987654321',
      owners: [
        {
          firstName: 'Jane',
          lastName: 'Doe',
          dateOfBirth: '1985-05-15',
          ssn: '987654321',
          email: '<EMAIL>',
          phone: '5551234567',
          address: {
            line1: '789 Main St',
            city: 'Test City',
            state: 'TX',
            zip: '75001',
          },
          ownershipPercentage: 100,
          creditScore: '730+',
        },
      ],
    });

    const response = await SELF.fetch(`${BASE_URL}/app/${testAppUuid}/sign`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ applicationFields: signedApplicationFields }),
    });

    expect(response.status).toBe(200);

    // Verify PII data was stored correctly including credit score
    const storedPII = await getAppPII(env, testAppUuid);
    expect(storedPII).toMatchObject({
      ein: '987654321',
      owners: [
        {
          dateOfBirth: '1985-05-15',
          ssn: '987654321',
        },
      ],
      version: env.VERSION,
    });
  });

  it('validates signature format and returns validation error for invalid signature', async () => {
    const invalidSignedApplicationFields = generateSignedApplicationData({
      signature: 'invalid-signature-format', // Invalid - not a PNG data URL
    });

    const response = await SELF.fetch(`${BASE_URL}/app/${testAppUuid}/sign`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ applicationFields: invalidSignedApplicationFields }),
    });

    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data.error).toContain('Validation Error');
  });

  it('validates credit score and returns validation error for invalid credit score', async () => {
    const invalidSignedApplicationFields = generateSignedApplicationData({
      owners: [
        {
          ...generateSignedApplicationData().owners[0],
          creditScore: 'invalid-score', // Invalid - not in enum
        },
      ],
    });

    const response = await SELF.fetch(`${BASE_URL}/app/${testAppUuid}/sign`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ applicationFields: invalidSignedApplicationFields }),
    });

    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data.error).toContain('Validation Error');
  });

  it('validates first owner must have credit score', async () => {
    const invalidSignedApplicationFields = generateSignedApplicationData({
      owners: [
        {
          firstName: 'Test',
          lastName: 'Owner',
          dateOfBirth: '1980-01-01',
          ssn: '123456789',
          email: '<EMAIL>',
          phone: '9876543210',
          address: {
            line1: '456 Owner St',
            city: 'Owner City',
            state: 'CA',
            zip: '54321',
          },
          ownershipPercentage: 100,
          // Missing creditScore for first owner
        },
      ],
    });

    const response = await SELF.fetch(`${BASE_URL}/app/${testAppUuid}/sign`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ applicationFields: invalidSignedApplicationFields }),
    });

    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data.error).toContain('Validation Error');
  });

  it('handles applications with two owners where only first has credit score', async () => {
    const signedApplicationFields = generateSignedApplicationData({
      owners: [
        {
          firstName: 'Owner',
          lastName: 'One',
          dateOfBirth: '1980-01-01',
          ssn: '111111111',
          email: '<EMAIL>',
          phone: '1111111111',
          address: {
            line1: '111 First St',
            city: 'First City',
            state: 'CA',
            zip: '11111',
          },
          ownershipPercentage: 60,
          creditScore: '680-729',
        },
        {
          firstName: 'Owner',
          lastName: 'Two',
          dateOfBirth: '1985-02-02',
          ssn: '222222222',
          email: '<EMAIL>',
          phone: '2222222222',
          address: {
            line1: '222 Second St',
            city: 'Second City',
            state: 'TX',
            zip: '22222',
          },
          ownershipPercentage: 40,
          // Second owner doesn't need credit score
        },
      ],
    });

    const response = await SELF.fetch(`${BASE_URL}/app/${testAppUuid}/sign`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ applicationFields: signedApplicationFields }),
    });

    expect(response.status).toBe(200);

    // Verify PII data for both owners was stored correctly
    const storedPII = await getAppPII(env, testAppUuid);
    expect(storedPII.owners).toHaveLength(2);
    expect(storedPII.owners[0]).toMatchObject({
      dateOfBirth: '1980-01-01',
      ssn: '111111111',
    });
    expect(storedPII.owners[1]).toMatchObject({
      dateOfBirth: '1985-02-02',
      ssn: '222222222',
    });

    // Verify sanitized application fields don't contain PII or credit score
    const savedApp = await ApplicationD1.get(env, testAppUuid);
    expect(savedApp.applicationFields.owners).toHaveLength(2);
    expect(savedApp.applicationFields.owners[0].ssn).toBeUndefined();
    expect(savedApp.applicationFields.owners[0].dateOfBirth).toBeUndefined();
    expect(savedApp.applicationFields.owners[1].ssn).toBeUndefined();
    expect(savedApp.applicationFields.owners[1].dateOfBirth).toBeUndefined();
  });

  it('handles missing applicationFields in request body', async () => {
    const response = await SELF.fetch(`${BASE_URL}/app/${testAppUuid}/sign`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({}), // Missing applicationFields
    });

    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data.error).toContain('Validation Error');
  });

  it('rejects signing application that is not in APP_SUBMITTED status', async () => {
    // Create another test application in wrong status
    const requestData = generateCreateAppRequest();
    const createResponse = await SELF.fetch(`${BASE_URL}/app`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestData),
    });
    const createData = await createResponse.json();
    const wrongStatusUuid = createData.uuid;

    // Try to sign without submitting first
    const signedApplicationFields = generateSignedApplicationData();

    const response = await SELF.fetch(`${BASE_URL}/app/${wrongStatusUuid}/sign`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ applicationFields: signedApplicationFields }),
    });

    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data.error).toContain("Application can't be signed");
  });

  it('returns existing application data when already signed', async () => {
    // Create and submit another application
    const requestData = generateCreateAppRequest();
    const createResponse = await SELF.fetch(`${BASE_URL}/app`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestData),
    });
    const createData = await createResponse.json();
    const alreadySignedUuid = createData.uuid;

    // Submit and sign the application
    const applicationFields = generateApplicationData();
    await ApplicationD1.update(env, alreadySignedUuid, { status: 'APP_STARTED' });

    await SELF.fetch(`${BASE_URL}/app/${alreadySignedUuid}/submit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ applicationFields }),
    });

    const signedApplicationFields = generateSignedApplicationData();
    await SELF.fetch(`${BASE_URL}/app/${alreadySignedUuid}/sign`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ applicationFields: signedApplicationFields }),
    });

    // Try to sign again
    const response = await SELF.fetch(`${BASE_URL}/app/${alreadySignedUuid}/sign`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ applicationFields: signedApplicationFields }),
    });

    expect(response.status).toBe(200);

    const data = await response.json();
    expect(data.data).toMatchObject({
      uuid: alreadySignedUuid,
      status: 'APP_SIGNED',
    });
  });

  it('handles database errors gracefully', async () => {
    vi.spyOn(ApplicationD1, 'update').mockRejectedValueOnce(new Error('Database connection failed'));

    const signedApplicationFields = generateSignedApplicationData();

    const response = await SELF.fetch(`${BASE_URL}/app/${testAppUuid}/sign`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ applicationFields: signedApplicationFields }),
    });

    expect(response.status).toBe(500);

    const data = await response.json();
    expect(data.error).toBe('Internal Server Error');
    expect(data.errorId).toBeDefined();
  });

  it('handles KV storage errors gracefully', async () => {
    vi.spyOn(kvModule, 'putAppPII').mockRejectedValueOnce(new Error('KV storage failed'));

    const signedApplicationFields = generateSignedApplicationData();

    const response = await SELF.fetch(`${BASE_URL}/app/${testAppUuid}/sign`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ applicationFields: signedApplicationFields }),
    });

    expect(response.status).toBe(500);

    const data = await response.json();
    expect(data.error).toBe('Internal Server Error');
  });

  it('handles email queue errors gracefully', async () => {
    vi.spyOn(SendToQueue, 'email').mockRejectedValueOnce(new Error('Email queue failed'));

    const signedApplicationFields = generateSignedApplicationData();

    const response = await SELF.fetch(`${BASE_URL}/app/${testAppUuid}/sign`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ applicationFields: signedApplicationFields }),
    });

    expect(response.status).toBe(500);

    const data = await response.json();
    expect(data.error).toBe('Internal Server Error');
  });

  it('handles Salesforce queue errors gracefully', async () => {
    vi.spyOn(SendToQueue, 'salseforce').mockRejectedValueOnce(new Error('Salesforce queue failed'));

    const signedApplicationFields = generateSignedApplicationData();

    const response = await SELF.fetch(`${BASE_URL}/app/${testAppUuid}/sign`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ applicationFields: signedApplicationFields }),
    });

    expect(response.status).toBe(500);

    const data = await response.json();
    expect(data.error).toBe('Internal Server Error');
  });

  it('validates all required fields from signed application schema', async () => {
    const invalidSignedApplicationFields = generateSignedApplicationData({
      businessName: '', // Invalid - too short
      signature: 'data:image/png;base64,validbase64', // Valid signature
    });

    const response = await SELF.fetch(`${BASE_URL}/app/${testAppUuid}/sign`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ applicationFields: invalidSignedApplicationFields }),
    });

    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data.error).toContain('Validation Error');
  });

  it('validates EIN format in signed application', async () => {
    const invalidSignedApplicationFields = generateSignedApplicationData({
      ein: '12345', // Invalid - wrong format
    });

    const response = await SELF.fetch(`${BASE_URL}/app/${testAppUuid}/sign`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ applicationFields: invalidSignedApplicationFields }),
    });

    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data.error).toContain('Validation Error');
  });

  it('validates email format in signed application', async () => {
    const invalidSignedApplicationFields = generateSignedApplicationData({
      businessEmail: 'invalid-email', // Invalid email format
    });

    const response = await SELF.fetch(`${BASE_URL}/app/${testAppUuid}/sign`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ applicationFields: invalidSignedApplicationFields }),
    });

    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data.error).toContain('Validation Error');
  });
});
