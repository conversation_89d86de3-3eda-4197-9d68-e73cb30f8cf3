import { createFactory } from 'hono/factory';
import { ApplicationD1 } from '../../db/applications';
import { SendToQueue } from '../../queues';
import { getMeta } from '../../utils';
import { AppError, cleanedApplication, extractAndSanitizePIIFields } from '../../utils/helpers';
import { requireApplication } from '../../middlewares/require-application';
import { signedApplicationSchema } from '../../schema/application';
import { zValidator } from '@hono/zod-validator';
import z from 'zod';
import { putAppPII } from '../../kv';

const factory = createFactory();

const validator = zValidator('json', z.object({ applicationFields: signedApplicationSchema }), async (result) => {
  if (!result.success) {
    console.log(JSON.stringify(result.error.issues, null, 2));
    throw new AppError('Validation Error: Invalid applicationFields', 400, 'validationError', result.error);
  }
});

export const signAppHandlers = factory.createHandlers(validator, requireApplication, async (c) => {
  const uuid = c.req.param('uuid');
  const timestamp = c.get('timestamp');
  let application = c.get('application');

  let applicationFields = c.req.valid('json').applicationFields;

  const { piiData, sanitizedApplicationFields } = extractAndSanitizePIIFields(applicationFields);

  const { status } = application;

  if (status !== 'APP_SUBMITTED') {
    if (['APP_SIGNED', 'APP_DOCS_PENDING', 'APP_COMPLETED'].includes(status)) {
      console.warn(`Application ${uuid} is already signed`);
      return c.json({ data: cleanedApplication(application) });
    }
    console.error(`Application status ${status} can't be signed`);
    throw new AppError(`Application can't be signed`, 400, 'signApp', `Status is ${status}`);
  }

  const columnsToUpdate = {
    signed_at: timestamp,
    status: 'APP_SIGNED',
    applicationFields: sanitizedApplicationFields,
    meta: {
      ...application.meta,
      signed: getMeta(c.req.raw, timestamp),
    },
  };

  application = { ...application, ...columnsToUpdate };

  await Promise.all([
    putAppPII(c.env, uuid, piiData),
    ApplicationD1.update(c.env, uuid, columnsToUpdate),
    SendToQueue.email(c.env, { application }),
    SendToQueue.salseforce(c.env, { application }),
  ]);

  return c.json({ data: cleanedApplication(application) });
});
