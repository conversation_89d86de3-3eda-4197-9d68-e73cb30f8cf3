import { SalesforceClient } from '../salesforce';
import { SalesforceUtils } from '../salesforce/utils';
import { getAppPII, getAppBankStatements } from '../kv';
import { ApplicationD1 } from '../db/applications';
import { SendToQueue } from '../queues';
import { Verify } from '../classes/verify';

const log = (...args) => console.log('[SALESFORCE]', ...args);

/**
 * Handle Salesforce queue messages
 * @param {Object} message - The message object containing application data
 * @param {Object} env - Environment variables and bindings
 */
export async function salesforceQueueHandler(message, env) {
  log('salesforceQueueHandler called');

  try {
    const { application } = message;

    if (!application) {
      console.error('SALESFORCE:\t', 'No application data in message');
      return;
    }

    if (application.version < 5) {
      log('Old application version:', application.version, ' skipping');
      return;
    }

    const {
      uuid,
      created_at,
      status,
      agent,
      domain,
      fastTrack,
      approvalAmount,
      reason,
      preQualifyFields: fields,
      applicationFields,
      utm,
      meta,
      meta: {
        initiated,
        initiated: { userAgent },
      },
    } = application;

    const deviceInfo = [userAgent?.device?.vendor, userAgent?.device?.model, userAgent?.os?.name, userAgent?.os?.version];
    const {
      utm_source,
      utm_campaign,
      utm_site,
      utm_rep,
      utm_af,
      utm_medium,
      utm_referrer,
      utm_form,
      utm_term,
      utm_content,
      utm_gclid,
      utm_dur: prequalDuration,
      utm_d: dealNumber,
    } = utm || {};

    log('Processing Salesforce data for application:', uuid);
    log('Application Status:', status);

    const appResumeUrl = SalesforceUtils.getResumeUrl(env, domain, status, uuid);

    // TODO: if no salesforce_id - create new deal and the continue with function to update object
    // TODO: create a function to prepare the SF object from an application object
    //    * this should handle all the SF field names, normalization and conditional fields like owner 2
    //    * as well as overriding emails/business names on dev mode
    //    * it should use SalesforceUtils.getDealStage for Stage__c
    let Deal__c = {};
    let SalesforceID = await ApplicationD1.getSalesforceID(env, uuid);
    const isNewApp = ['PREQUAL_APPROVED', 'PREQUAL_DENIED', 'PREQUAL_FAST_TRACK'].includes(status);

    if (!isNewApp && !SalesforceID) {
      throw new Error('SALESFORCE_ID_NOT_FOUND');
    }
    const LogName = `${env.DEV_MODE ? '[DEV] ' : ''}${status}`;

    switch (status) {
      case 'PREQUAL_APPROVED':
      case 'PREQUAL_DENIED':
      case 'PREQUAL_FAST_TRACK':
        Deal__c = {
          Stage__c: SalesforceUtils.getDealStage(status),
          Portal_Status__c: status,
          Method__c: 'Portal',
          // Prequal Fields
          Funding_Purpose__c: fields.purpose,
          Funding_Timeline__c: fields.timeline,
          Top_Priority__c: fields.topPriority,
          Estimated_Fico__c: fields.estimatedFICO,
          Monthly_Revenue_Range__c: fields.monthlyRevenue,
          Annual_Revenue__c: fields.annualRevenue,
          Business_Revenue__c: SalesforceUtils.parseMonthlyRevenue(fields.monthlyRevenue, fields.annualRevenue),
          Credit_Score_Owner1__c: SalesforceUtils.getCreditScore(fields.estimatedFICO),
          First_Name_O1__c: fields.firstName,
          Last_Name_O1__c: fields.lastName,
          Email__c: fields.email,
          Phone__c: fields.phone,
          Business_Phone__c: fields.phone,
          Requested_Amount__c: fields.fundingAmount,
          Business_Legal_Name__c: fields.businessName,
          Business_Start_Date__c: fields.businessStartDate,
          // App Details
          UUID__c: uuid,
          FastTrack__c: fastTrack,
          Domain__c: domain,
          OwnerId: agent?.id,
          PortalRep__c: agent?.id,
          Date_Moved_to_Interested__c: created_at,
          Denial_Reason__c: reason,
          Pre_Qual_Amount__c: approvalAmount,
          App_Resume_URL__c: appResumeUrl,
          // Metadata
          IP__c: initiated.ip,
          Country__c: initiated.country,
          City__c: initiated.city,
          Timezone__c: initiated.timezone,
          Browser__c: initiated.userAgent?.browser?.name,
          Device__c: deviceInfo.filter(Boolean).join(' '),
          // UTM
          UTM_Source__c: utm_source,
          UTM_Campaign__c: utm_campaign,
          UTM_Site__c: utm_site,
          UTM_Rep__c: utm_rep,
          UTM_Affiliate__c: utm_af,
          UTM_Medium__c: utm_medium,
          UTM_Referrer__c: utm_referrer,
          UTM_Form__c: utm_form,
          UTM_Term__c: utm_term,
          UTM_Content__c: utm_content,
          UTM_GCLID__c: utm_gclid,
          // Prequal Form Duration
          Prequal_Duration__c: parseInt(prequalDuration),
        };

        if (env.DEV_MODE) {
          Deal__c.Business_Legal_Name__c = `[DEV] ${fields.businessName}`;
          Deal__c.Email__c = `DEV-${fields.email}`;
        }

        const [emailVerification, phoneVerification] = await Promise.all([
          // catch null to still allow processing to continue in case of external API errors
          Verify.email(env, fields.email).catch((err) => {
            console.warn('Email verification failed:', err);
            return null;
          }),
          Verify.phone(env, fields.phone).catch((err) => {
            console.warn('Phone verification failed:', err);
            return null;
          }),
        ]);

        Deal__c.Verify_Email_Score__c = emailVerification?.score;
        Deal__c.Verify_Email_Disposable__c = emailVerification?.disposable;
        Deal__c.Verify_Email_State__c = emailVerification?.state;

        const validPhone = phoneVerification?.valid;
        Deal__c.Verify_Phone_Valid__c = validPhone;
        Deal__c.Verify_Phone_Carrier__c = validPhone === false ? 'INVALID' : phoneVerification?.carrier_name;
        Deal__c.Verify_Phone_Type__c = validPhone === false ? 'INVALID' : phoneVerification?.type;

        // If it's FastTrack + existing deal number is provided, check if it exists and update if necessary
        if (fastTrack && /^\d+$/.test(dealNumber)) {
          try {
            log(`FastTrack App is linked with pre-existing deal: Deal-${dealNumber}`);
            const existingDeal = await SalesforceClient.getDealByNumber(env, dealNumber);

            // Same Rep + Email OR Phone OR Business Name match
            const sameRep = existingDeal?.OwnerId === Deal__c.OwnerId;
            const matches = {
              email: fields.email?.toLowerCase() === existingDeal?.Email__c?.toLowerCase(),
              phone: fields.phone?.replace(/\D/g, '') === existingDeal?.Phone__c?.replace(/\D/g, ''),
              businessName: fields.businessName?.toLowerCase() === existingDeal?.Business_Legal_Name__c?.toLowerCase(),
            };

            function anyMatch(obj) {
              return Object.values(obj).some(Boolean);
            }
            const isMatch = sameRep && anyMatch(matches);

            if (isMatch) {
              SalesforceID = existingDeal.Id;
              await SalesforceClient.updateDeal(env, SalesforceID, Deal__c);
            } else log('MISMATCH: Pre-existing deal mismatch, creating new deal instead');
          } catch (error) {
            log('Error updating pre-existing deal, creating new deal instead:', error.message);
          }
        }

        // If no Salesforce ID, create a new deal - this handles if the existing deal didn't match or wasn't found
        if (!SalesforceID) {
          log('Creating Deal in SF:', Deal__c);
          const data = await SalesforceClient.createDeal(env, Deal__c);
          log('Deal Created in SF', JSON.stringify(data));
          SalesforceID = data.id;
        }

        log('Deal ID:', SalesforceID);

        await ApplicationD1.update(env, uuid, { salesforce_id: SalesforceID });

        application.salesforce_id = SalesforceID;
        await SendToQueue.admin(env, { application });

        await SalesforceClient.APIUpdateLog(env, SalesforceID, LogName, SalesforceUtils.printMeta(meta.initiated), application);
        break;

      case 'APP_SUBMITTED':
        const { address, owners } = applicationFields;
        const pii = await getAppPII(env, uuid);
        const hasTwoOwners = owners.length === 2;

        Deal__c = {
          Stage__c: SalesforceUtils.getDealStage(status),
          Portal_Status__c: status,
          // App Fields
          Business_Legal_Name__c: applicationFields.businessName,
          Business_DBA_Name__c: applicationFields.dbaName,
          Website__c: applicationFields.website,
          Legal_Entity_Type__c: applicationFields.entityType,
          Industry__c: applicationFields.industry,
          Business_Start_Date__c: applicationFields.businessStartDate,
          Business_Phone__c: applicationFields.phone,
          Business_Email__c: applicationFields.businessEmail,
          Email__c: applicationFields.email,
          Phone__c: applicationFields.phone,
          Business_Street__c: `${address.line1} ${address.line2}`,
          Business_City__c: address.city,
          Business_State__c: address.state,
          Business_Zip__c: address.zip,
          // Owner Fields
          First_Name_O1__c: owners[0].firstName,
          Last_Name_O1__c: owners[0].lastName,
          Email_O1__c: owners[0].email,
          Phone_O1__c: owners[0].phone,
          Home_Address_O1__c: `${owners[0].address.line1} ${owners[0].address.line2}`,
          Home_City_O1__c: owners[0].address.city,
          Home_State_O1__c: owners[0].address.state,
          Home_Zip_O1__c: owners[0].address.zip,
          Percent_of_Ownership_O1__c: owners[0].ownershipPercentage,
          // PII
          EIN__c: pii?.ein,
          SSN_O1__c: pii?.owners[0]?.ssn,
          DOB_O1__c: pii?.owners[0]?.dateOfBirth,
        };
        // If there are two owners on the deal, inject O2 fields
        if (hasTwoOwners) {
          const owner2Fields = {
            First_Name_O2__c: owners[1].firstName,
            Last_Name_O2__c: owners[1].lastName,
            Email_O2__c: owners[1].email,
            Phone_O2__c: owners[1].phone,
            Home_Address_O2__c: `${owners[1].address.line1} ${owners[1].address.line2}`,
            Home_City_O2__c: owners[1].address.city,
            Home_State_O2__c: owners[1].address.state,
            Home_Zip_O2__c: owners[1].address.zip,
            Percent_of_Ownership_O2__c: owners[1].ownershipPercentage,
            // Owner #2 PII
            SSN_O2__c: pii?.owners[1]?.ssn,
            DOB_O2__c: pii?.owners[1]?.dateOfBirth,
          };
          Object.assign(Deal__c, owner2Fields);
        }

        if (env.DEV_MODE) {
          Deal__c.Business_Legal_Name__c = `[DEV] ${applicationFields.businessName}`;
          Deal__c.Email__c = `DEV-${applicationFields.businessEmail}`;
        }

        // PII Fields are masked here so they aren't present in logs etc.
        log(`Updating Deal ${SalesforceID} in SF:`, SalesforceUtils.maskFields(Deal__c));
        await SalesforceClient.updateDeal(env, SalesforceID, Deal__c);
        await SalesforceClient.APIUpdateLog(env, SalesforceID, LogName, SalesforceUtils.printMeta(meta.submitted), application);

        break;

      case 'APP_SIGNED':
        Deal__c = { Stage__c: SalesforceUtils.getDealStage(status), Portal_Status__c: status };

        const signedAppBase64 = ``; // TODO
        const fileName = `App - ${applicationFields.businessName}.pdf`;

        const linkedFile = await SalesforceClient.uploadLinkedFile(env, SalesforceID, fileName, signedAppBase64);
        log(fileName, 'uploaded and linked to deal', linkedFile);

        log(`Updating Deal ${SalesforceID} in SF:`, Deal__c);
        await SalesforceClient.updateDeal(env, SalesforceID, Deal__c);
        await SalesforceClient.APIUpdateLog(env, SalesforceID, LogName, SalesforceUtils.printMeta(meta.signed), application);

        break;

      case 'APP_DOCS_PENDING':
      case 'APP_COMPLETED':
        const { hasBankStatements } = message;

        Deal__c = {
          Stage__c: SalesforceUtils.getDealStage(status),
          Portal_Status__c: status,
          App_Resume_URL__c: appResumeUrl,
        };

        if (hasBankStatements) {
          log('Uploading bank statements');
          const storedBankStatements = await getAppBankStatements(env, uuid);

          // Switched from Promise.all - storedBankStatements.map to let them process one-by-one
          for (const { name, dataUrl } of storedBankStatements) {
            const base64Content = dataUrl.replace(/^data:.*;base64,/, '');
            await SalesforceClient.uploadLinkedFile(env, SalesforceID, name, base64Content);
          }
        }

        log(`Updating Deal ${SalesforceID} in SF:`, Deal__c);
        await SalesforceClient.updateDeal(env, SalesforceID, Deal__c);
        await SalesforceClient.APIUpdateLog(env, SalesforceID, LogName, SalesforceUtils.printMeta(meta.completed), application);

        break;

      case 'APP_STARTED':
        Deal__c = { Portal_Status__c: status };

        log(`Updating Deal ${SalesforceID} in SF:`, Deal__c);
        await SalesforceClient.updateDeal(env, SalesforceID, Deal__c);
        await SalesforceClient.APIUpdateLog(env, SalesforceID, LogName, SalesforceUtils.printMeta(meta.started), application);
        break;
      case 'APP_EDITING':
        Deal__c = { Portal_Status__c: status };

        log(`Updating Deal ${SalesforceID} in SF:`, Deal__c);
        await SalesforceClient.updateDeal(env, SalesforceID, Deal__c);
        await SalesforceClient.APIUpdateLog(env, SalesforceID, LogName, SalesforceUtils.printMeta(meta.edited), application);
        break;
      default:
        log(`No action required for this status:`, status);
        break;
    }

    log('Salesforce processing completed successfully');
  } catch (error) {
    log(error.message);
    throw error;
  }
}
