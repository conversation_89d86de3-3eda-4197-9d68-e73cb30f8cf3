import { z } from 'zod';

const purposeEnum = ['Expansion', 'WorkingCapital', 'Payroll', 'Equipment', 'BuyABusiness', 'RealEstate', 'StartABusiness', 'Other'];

const topPriorityEnum = ['size', 'speed', 'cost'];

const timelineEnum = ['asap', 'week', 'month', 'longterm'];

const monthlyRevenueEnum = ['0-10000', '10000-25000', '25000-50000', '************', '100000-250000', '250000-500000', '500000+'];

export const estimatedFICOEnum = ['300-499', '500-579', '580-629', '630-679', '680-729', '730+'];

export const preQualifySchema = z.object({
  fundingAmount: z.number().min(5000).max(1500000),
  purpose: z.enum(purposeEnum),
  topPriority: z.enum(topPriorityEnum),
  timeline: z.enum(timelineEnum),
  businessName: z.string().min(2).max(100),
  monthlyRevenue: z.enum(monthlyRevenueEnum),
  annualRevenue: z.number().max(120000).nullable().optional(),
  businessStartDate: z.string().date(),
  firstName: z.string().min(2).max(50),
  lastName: z.string().min(2).max(50),
  email: z.string().email(),
  phone: z.string().regex(/^\+?[\d-]{10,}$/),
  estimatedFICO: z.enum(estimatedFICOEnum),
  consent: z.literal(true, {
    errorMap: () => ({ message: 'You must agree to the terms and conditions' }),
  }),
  currentStep: z.number().int().min(0).optional(),
});

export const fastTrackPreQualifySchema = z.object({
  businessName: z.string().min(2).max(100),
  firstName: z.string().min(2).max(50),
  lastName: z.string().min(2).max(50),
  email: z.string().email(),
  phone: z.string().regex(/^\+?[\d-]{10,}$/),
  estimatedFICO: z.enum(estimatedFICOEnum),
  consent: z.literal(true, {
    errorMap: () => ({ message: 'You must agree to the terms and conditions' }),
  }),
});
