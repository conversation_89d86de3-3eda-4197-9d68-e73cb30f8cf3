<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Signed Application Document</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Times New Roman', serif;
        font-size: 16px;
        line-height: 1.4;
        color: #000;
        background: #fff;
        margin: 0;
        padding: 6px;
      }

      .document {
        max-width: 8.5in;
        margin: 0 auto;
        background: #fff;
        position: relative;
        padding-bottom: 15px;
      }

      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #000;
        padding-bottom: 4px;
        margin-bottom: 8px;
      }

      .header-left {
        flex: 1;
      }

      .header-right {
        flex: 1;
        text-align: right;
      }

      .logo img {
        height: 32px;
        max-width: 130px;
      }

      .company-info {
        font-size: 14px;
        line-height: 1.3;
      }

      .company-info .phone {
        font-weight: bold;
        margin-bottom: 2px;
      }

      .document-title {
        text-align: center;
        font-size: 20px;
        font-weight: bold;
        text-transform: uppercase;
        margin: 5px 0;
        letter-spacing: 1px;
      }

      .section {
        margin-bottom: 6px;
      }

      .section-title {
        font-size: 17px;
        font-weight: bold;
        text-transform: uppercase;
        border-bottom: 1px solid #000;
        padding-bottom: 2px;
        margin-bottom: 4px;
        letter-spacing: 0.5px;
      }

      .field-row {
        display: flex;
        margin-bottom: 1px;
        align-items: baseline;
      }

      .field-label {
        font-weight: bold;
        min-width: 120px;
        margin-right: 8px;
        font-size: 15px;
      }

      .field-value {
        flex: 1;
        border-bottom: 1px dotted #666;
        padding-bottom: 1px;
        min-height: 12px;
        font-size: 15px;
      }

      .address-block {
        margin-left: 128px;
        margin-top: 1px;
        font-size: 15px;
      }

      .owner-section {
        border: 1px solid #ddd;
        padding: 4px;
        margin-bottom: 4px;
        background-color: #fafafa;
      }

      .owner-title {
        font-weight: bold;
        font-size: 16px;
        margin-bottom: 2px;
        text-decoration: underline;
      }

      .signature-section {
        margin-top: 6px;
        border-top: 1px solid #000;
        padding-top: 5px;
      }

      .signature-container {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        margin-top: 6px;
      }

      .signature-block {
        width: 45%;
      }

      .signature-image {
        border: 1px solid #ccc;
        height: 35px;
        width: 100%;
        background-color: #fff;
        margin-bottom: 2px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
      }

      .signature-image img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
      }

      .signature-line {
        border-bottom: 1px solid #000;
        margin-bottom: 2px;
        height: 10px;
      }

      .signature-label {
        font-size: 14px;
        text-align: center;
        font-weight: bold;
      }

      .date-block {
        width: 30%;
      }

      .footer {
        margin-top: 6px;
        font-size: 13px;
        color: #666;
        border-top: 1px solid #ccc;
        padding-top: 3px;
        text-align: center;
      }

      .two-column {
        display: flex;
        gap: 10px;
      }

      .column {
        flex: 1;
      }

      @media print {
        body {
          padding: 0;
          margin: 0;
        }

        .document {
          margin: 0;
          padding: 0;
          padding-bottom: 10px;
        }

        .footer {
          margin-top: 5px;
          padding-top: 3px;
          position: static;
        }

        @page {
          margin: 0.5in;
          size: letter;
        }
      }
    </style>
  </head>
  <body>
    <div class="document">
      <!-- Header -->
      <div class="header">
        <div class="header-left">
          <div class="logo">
            <img src="https://static.pinnaclefunding.com/app-logo.svg" alt="Pinnacle Funding" />
          </div>
        </div>
        <div class="header-right">
          <div class="company-info">
            <div class="phone">+****************</div>
            <div><EMAIL></div>
            <div>https://pinnaclefunding.com/</div>
          </div>
        </div>
      </div>

      <!-- Document Title -->
      <div class="document-title">Business Funding Application</div>

      <!-- Business Information Section -->
      <div class="section">
        <div class="section-title">Business Information</div>

        <div class="field-row">
          <div class="field-label">Business Name:</div>
          <div class="field-value">{{applicationFields.businessName}}</div>
        </div>

        {{#applicationFields.dbaName}}
        <div class="field-row">
          <div class="field-label">DBA Name:</div>
          <div class="field-value">{{.}}</div>
        </div>
        {{/applicationFields.dbaName}}

        <div class="field-row">
          <div class="field-label">Entity Type:</div>
          <div class="field-value">{{applicationFields.entityType}}</div>
        </div>

        <div class="field-row">
          <div class="field-label">EIN:</div>
          <div class="field-value">{{applicationFields.ein}}</div>
        </div>

        <div class="field-row">
          <div class="field-label">Industry:</div>
          <div class="field-value">{{applicationFields.industry}}</div>
        </div>

        <div class="field-row">
          <div class="field-label">Business Start Date:</div>
          <div class="field-value">{{applicationFields.businessStartDate}}</div>
        </div>

        {{#applicationFields.website}}
        <div class="field-row">
          <div class="field-label">Website:</div>
          <div class="field-value">{{.}}</div>
        </div>
        {{/applicationFields.website}}

        <div class="field-row">
          <div class="field-label">Business Phone:</div>
          <div class="field-value">{{applicationFields.businessPhone}}</div>
        </div>

        <div class="field-row">
          <div class="field-label">Business Email:</div>
          <div class="field-value">{{applicationFields.businessEmail}}</div>
        </div>

        <div class="field-row">
          <div class="field-label">Business Address:</div>
          <div class="field-value">{{applicationFields.address.line1}}</div>
        </div>
        {{#applicationFields.address.line2}}
        <div class="address-block">{{.}}</div>
        {{/applicationFields.address.line2}}
        <div class="address-block">
          {{applicationFields.address.city}}, {{applicationFields.address.state}} {{applicationFields.address.zip}}
        </div>
      </div>

      <!-- Owner Information Section -->
      <div class="section">
        <div class="section-title">Owner Information</div>

        {{#applicationFields.owners}}
        <div class="owner-section">
          <div class="owner-title">Owner {{@index_1}}</div>

          <div class="two-column">
            <div class="column">
              <div class="field-row">
                <div class="field-label">Name:</div>
                <div class="field-value">{{firstName}} {{lastName}}</div>
              </div>

              <div class="field-row">
                <div class="field-label">Date of Birth:</div>
                <div class="field-value">{{dateOfBirth}}</div>
              </div>

              <div class="field-row">
                <div class="field-label">Email:</div>
                <div class="field-value">{{email}}</div>
              </div>

              <div class="field-row">
                <div class="field-label">Phone:</div>
                <div class="field-value">{{phone}}</div>
              </div>
            </div>

            <div class="column">
              <div class="field-row">
                <div class="field-label">Ownership %:</div>
                <div class="field-value">{{ownershipPercentage}}%</div>
              </div>

              {{#creditScore}}
              <div class="field-row">
                <div class="field-label">Credit Score Range:</div>
                <div class="field-value">{{.}}</div>
              </div>
              {{/creditScore}}

              <div class="field-row">
                <div class="field-label">Address:</div>
                <div class="field-value">{{address.line1}}</div>
              </div>
              {{#address.line2}}
              <div class="address-block">{{.}}</div>
              {{/address.line2}}
              <div class="address-block">{{address.city}}, {{address.state}} {{address.zip}}</div>
            </div>
          </div>
        </div>
        {{/applicationFields.owners}}
      </div>

      <!-- Signature Section -->
      <div class="signature-section">
        <div class="section-title">Digital Signature</div>

        <div class="signature-container">
          <div class="signature-block">
            <div class="signature-image">
              {{#applicationFields.signature}}
              <img src="{{.}}" alt="Digital Signature" />
              {{/applicationFields.signature}} {{^applicationFields.signature}}
              <span style="color: #999">No signature available</span>
              {{/applicationFields.signature}}
            </div>
            <div class="signature-line"></div>
            <div class="signature-label">Applicant Signature</div>
          </div>

          <div class="date-block">
            <div class="signature-line"></div>
            <div class="signature-label">Date: {{signedDate}}</div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="footer">
        <div>This document was digitally signed and submitted through Pinnacle Funding's secure application portal.</div>
        <div>Document generated on {{generatedDate}} | Application ID: {{uuid}}</div>
      </div>
    </div>
  </body>
</html>
